import { deleteAccessTokenCookies } from "./cookiesCollection"
import { hasValidAuthTokens, clearAuthTokens } from "./authUtils"

// ⚠️ DEPRECATED: These functions use insecure localStorage for authentication
// Use authUtils.js functions instead for secure authentication

/**
 * @deprecated Use authUtils.hasValidAuthTokens() instead
 * This function is kept for backward compatibility but should not be used for auth checks
 */
export const setLoginToken = (token) => {
  console.warn('⚠️ setLoginToken is deprecated. Use secure cookie-based authentication instead.')
  window.localStorage.setItem('username', token)
}

export const getItem = (key) => window.localStorage.getItem(key)

export const setItem = (key, value) => window.localStorage.setItem(key, value)

export const removeItem = (key) => window.localStorage.removeItem(key)

/**
 * @deprecated Use authUtils.hasValidAuthTokens() instead
 * This function is kept for backward compatibility but should not be used for auth checks
 */
export const getLoginToken = () => {
  console.warn('⚠️ getLoginToken is deprecated. Use authUtils.hasValidAuthTokens() instead.')
  return window.localStorage.getItem('username') || ''
}

/**
 * @deprecated Use authUtils.clearAuthTokens() instead
 */
export const removeLoginToken = () => {
  console.warn('⚠️ removeLoginToken is deprecated. Use authUtils.clearAuthTokens() instead.')
  clearAuthTokens()
}
