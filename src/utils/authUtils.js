import { getCookie, deleteCookie } from './cookiesCollection'
import { deleteAccessTokenCookies } from './cookiesCollection'

/**
 * Secure authentication utilities that validate real tokens
 * instead of relying on localStorage values
 */

/**
 * Check if user has valid authentication tokens
 * @returns {boolean} True if user has valid tokens
 */
export const hasValidAuthTokens = () => {
  const accessToken = getCookie('accessToken')
  const pathCookie = getCookie('path')
  
  // Must have both access token and path permission
  return !!(accessToken && pathCookie)
}

/**
 * Get the access token from secure cookie
 * @returns {string} Access token or empty string
 */
export const getAccessToken = () => {
  return getCookie('accessToken') || ''
}

/**
 * Get the path permission cookie
 * @returns {string} Path cookie or empty string
 */
export const getPathCookie = () => {
  return getCookie('path') || ''
}

/**
 * Check if user has specific permissions
 * @param {string} requiredPath - Required path permission
 * @returns {boolean} True if user has permission
 */
export const hasPathPermission = (requiredPath = '/home') => {
  const pathCookie = getPathCookie()
  return pathCookie === requiredPath
}

/**
 * Validate token format (basic validation)
 * @param {string} token - Token to validate
 * @returns {boolean} True if token format is valid
 */
export const isValidTokenFormat = (token) => {
  if (!token || typeof token !== 'string') return false
  
  // Basic JWT format check (3 parts separated by dots)
  const parts = token.split('.')
  if (parts.length === 3) {
    return true // Likely a JWT
  }
  
  // Or check for session token format (alphanumeric, minimum length)
  if (token.length >= 20 && /^[a-zA-Z0-9]+$/.test(token)) {
    return true // Likely a session token
  }
  
  return false
}

/**
 * Get authentication status with token validation
 * @returns {object} Authentication status object
 */
export const getAuthStatus = () => {
  const accessToken = getAccessToken()
  const pathCookie = getPathCookie()
  const hasTokens = hasValidAuthTokens()
  const validTokenFormat = isValidTokenFormat(accessToken)
  
  return {
    hasTokens,
    hasAccessToken: !!accessToken,
    hasPathPermission: !!pathCookie,
    validTokenFormat,
    isLikelyAuthenticated: hasTokens && validTokenFormat,
    accessToken: accessToken ? '[REDACTED]' : null, // Don't expose actual token
    pathPermission: pathCookie
  }
}

/**
 * Clear all authentication tokens and data
 */
export const clearAuthTokens = () => {
  // Clear cookies
  deleteCookie('accessToken')
  deleteCookie('path')
  deleteAccessTokenCookies('accessToken')
  
  // Clear localStorage (but preserve allowedUserAccess)
  const allowedUserAccess = localStorage.getItem('allowedUserAccess')
  localStorage.clear()
  if (allowedUserAccess) {
    localStorage.setItem('allowedUserAccess', allowedUserAccess)
  }
}

/**
 * Check if authentication tokens are expired (client-side estimation)
 * Note: This is just a client-side check, server validation is authoritative
 * @returns {boolean} True if tokens might be expired
 */
export const areTokensLikelyExpired = () => {
  const accessToken = getAccessToken()
  
  if (!accessToken) return true
  
  // If it's a JWT, try to decode and check expiration
  if (isValidTokenFormat(accessToken) && accessToken.includes('.')) {
    try {
      const payload = JSON.parse(atob(accessToken.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      
      if (payload.exp && payload.exp < currentTime) {
        return true // Token is expired
      }
    } catch (error) {
      // If we can't decode, assume it might be expired
      console.warn('Could not decode token for expiration check:', error)
      return false // Don't assume expired if we can't check
    }
  }
  
  return false // Can't determine expiration
}

/**
 * Get user info from token (if it's a JWT)
 * @returns {object|null} User info or null
 */
export const getUserInfoFromToken = () => {
  const accessToken = getAccessToken()
  
  if (!accessToken || !accessToken.includes('.')) {
    return null // Not a JWT
  }
  
  try {
    const payload = JSON.parse(atob(accessToken.split('.')[1]))
    
    // Return safe user info (don't expose sensitive data)
    return {
      userId: payload.sub || payload.userId || payload.id,
      email: payload.email,
      username: payload.username || payload.name,
      roles: payload.roles || payload.permissions,
      exp: payload.exp,
      iat: payload.iat
    }
  } catch (error) {
    console.warn('Could not decode user info from token:', error)
    return null
  }
}

/**
 * Debug function to log authentication status (development only)
 */
export const debugAuthStatus = () => {
  if (import.meta.env.DEV) {
    const status = getAuthStatus()
    console.group('🔐 Authentication Status')
    console.log('Has Tokens:', status.hasTokens)
    console.log('Has Access Token:', status.hasAccessToken)
    console.log('Has Path Permission:', status.hasPathPermission)
    console.log('Valid Token Format:', status.validTokenFormat)
    console.log('Likely Authenticated:', status.isLikelyAuthenticated)
    console.log('Path Permission:', status.pathPermission)
    console.log('Tokens Likely Expired:', areTokensLikelyExpired())
    
    const userInfo = getUserInfoFromToken()
    if (userInfo) {
      console.log('User Info from Token:', userInfo)
    }
    console.groupEnd()
  }
}

export default {
  hasValidAuthTokens,
  getAccessToken,
  getPathCookie,
  hasPathPermission,
  isValidTokenFormat,
  getAuthStatus,
  clearAuthTokens,
  areTokensLikelyExpired,
  getUserInfoFromToken,
  debugAuthStatus
}
