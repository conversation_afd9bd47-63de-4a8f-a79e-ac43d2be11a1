import React, { useEffect, useCallback, lazy, Suspense } from 'react'
import { Grid } from '@mui/material'
import { getItem, getLoginToken } from '../../utils/storageUtils'
import useStyles from './../Lobby/Lobby.styles'
import { useUserStore } from '../../store/useUserSlice'
import { usePortalStore } from '../../store/userPortalSlice'
import ProviderList from './ProviderSection/ProviderList'
import JackpotBadge from '../Jackpot/JackpotBadge'
import BannerManagement from '../../components/BannerManagement'
import LobbyFilter from './LobbyFilter'
import { useBannerStore } from '../../store/useBannerSlice'
import SeoHead from '../../utils/seoHead'
import LiveWinners from './LiveWinners'
import { useGamesStore } from '../../store/useGamesSlice'
import useProfileAndAuth from '../../hooks/useProfileAndAuth.jsx'
import { useLocation } from 'react-router-dom'

const LobbyFaq = lazy(() => import('./LobbyFaq'))
const GamesGrid = lazy(() => import('./GamesGrid'))
const SpecialPurchaseModal = lazy(() => import('../../components/SpecialPurchaseModal'))
const DailyBonusTimer = lazy(() => import('../../components/CountDownTimer/dailyBonusTimer'))
const TimerAnimation = lazy(() => import('../Lobby/components/TimerAnimation'))

const LandingPage = () => {
  const classes = useStyles()
  const auth = useUserStore()
  const userDetails = useUserStore((state) => state)
  const portalStore = usePortalStore()
  const { lobbyBanners } = useBannerStore()
  const selectedSubCat = useGamesStore((state) => state.selectedSubCat)
  const location = useLocation()

  // Use the profile and auth hook for bonus logic
  const { getProfileMutation } = useProfileAndAuth()

  const handleSpPackage = useCallback(() => {
    portalStore.openPortal(() => <SpecialPurchaseModal />, 'termsNConditionModal')
  }, [portalStore])
                                                                                                                                                                  
  // Trigger profile and bonus logic when user is logged in
  useEffect(() => {
    const username = localStorage.getItem('username')
    const isLoggedIn = !!getLoginToken() || auth.isAuthenticate

    if (username && userDetails.userDetails === null && isLoggedIn) {
      if (!location.pathname.endsWith('verifyEmail')) {
        getProfileMutation.mutate()
      }
    }
  }, [auth.isAuthenticate, userDetails.userDetails, location.pathname, getProfileMutation])

  useEffect(() => {
    const specialPackagePopup = getItem('special_package')
    const username = getItem('username')
    const isLoggedIn = !!getLoginToken() || auth.isAuthenticate
    const bonusApplicable = userDetails.userDetails?.welcomePurchaseBonusApplicable

    if (!username && !specialPackagePopup && bonusApplicable && isLoggedIn) {
      handleSpPackage()
    }
  }, [auth, userDetails, handleSpPackage])

  const showBonusTimer =
    getItem('special_package') === 'true' &&
    userDetails.userDetails?.welcomePurchaseBonusApplicable &&
    (!!getLoginToken() || auth?.isAuthenticate)

  return (
    <main className={classes.wrapper} style={{ minHeight: '100vh' }}>
      <SeoHead
        title='Play Free Casino Games Online - Earn SC & Unlock Loyalty Rewards'
        description='Join The Money Factory to enjoy free slots, table games, and exclusive TMF Originals. Track your loyalty level, earn redeemable SC, and unlock exciting promotions. Play for fun. No purchase needed.'
        // keywords={['about us', 'company', 'mission', 'team']}
      />
      <JackpotBadge />
      <BannerManagement bannerData={lobbyBanners} />
      <LobbyFilter />
     {selectedSubCat!=='Favorite Games'  &&  selectedSubCat!=='Recently Played'  && <LiveWinners />}
      {selectedSubCat!=='Favorite Games'  &&  selectedSubCat !=='Recently Played'  && <ProviderList />}
      <Suspense fallback={<div>Loading games...</div>}>
        <GamesGrid />
      </Suspense>
      {showBonusTimer && (
        <Suspense fallback={<div>Loading bonus timer...</div>}>
          <Grid className={classes.timerWrapper} onClick={handleSpPackage}>
            <Grid className='timer-card'>
              <DailyBonusTimer eventDateTime={userDetails.userDetails?.welcomePurchaseBonusEndTime} />
            </Grid>
            <Grid className='timer-bottom-box'>
              <TimerAnimation />
            </Grid>
          </Grid>
        </Suspense>
      )}
      <Suspense fallback={<div>Loading FAQ...</div>}>
        <LobbyFaq />
      </Suspense>
    </main>
  )
}

export default LandingPage
