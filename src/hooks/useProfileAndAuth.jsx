import { Suspense, lazy } from 'react'
import { useLocation } from 'react-router-dom'
import { useGetProfileMutation } from '../reactQuery'
import {
  useGetDailyBonusMutation,
  useGetFreeSpinMutation,
  useGetPromotionBonusMutation,
  useGetWelcomeBonusMutation
} from '../reactQuery/bonusQuery'
import {
  useJackpotStore,
  usePortalStore,
  useUserStore
} from '../store/store'
import { getCookie } from '../utils/cookiesCollection'
import ModalLoader from '../components/ui-kit/ModalLoader'

// Lazy load modal components for better performance
const DailyBonus = lazy(() => import('../components/DailyBonus'))
const WelcomeBonus = lazy(() => import('../components/WelcomeBonus'))
const UserNameModal = lazy(() => import('../components/Modal/Signup/UserNameModal'))
const PromotionBonus = lazy(() => import('../components/DailyBonus/promotionBonus'))
const TermAndCondition = lazy(() => import('../components/Modal/TermAndCondition/TermAndCondition'))
const ScratchCardComponent = lazy(() => import('../components/ScratchCard/ScratchCardComponent'))
const FreeSpinModal = lazy(() => import('../components/FreeSpinModal/FreeSpinModal'))

/**
 * Custom hook for handling profile fetching and authentication-related modals
 * This hook manages the complex logic of showing various bonus modals based on user state
 */
const useProfileAndAuth = () => {
  const location = useLocation()
  const user = useUserStore((state) => state)
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)
  const portalStore = usePortalStore((state) => state)
  const { setJackpotOn, setJackpotMultiplier } = useJackpotStore()

  // Get URL parameters and cookies
  const params = new URLSearchParams(window.location.search)
  const paymentMethod = params.get('paymentMethod')
  const paymentMethodFromStore = window.localStorage.getItem('paymentMethod')
  const pathCookie = getCookie('path')
  const gamePlayUrl = location.pathname.startsWith('/game-play')

  /** Bonus Mutations **/
  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      if (res?.data?.data) {
        const resetData = res?.data?.data?.remainingTime
        if (isAuthenticate) {
          portalStore.openPortal(
            () => (
              <Suspense fallback={<ModalLoader />}>
                <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />
              </Suspense>
            ),
            'bonusStreak'
          )
        }
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <WelcomeBonus welcomeBonusData={res?.data?.data} />
            </Suspense>
          ),
          'bonusModal'
        )
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetPromotionBonus = useGetPromotionBonusMutation({
    onSuccess: (res) => {
      const { scAmount, gcAmount } = res?.data?.data || {}
      if (scAmount || gcAmount) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <PromotionBonus promotionBonusData={res?.data?.data} />
            </Suspense>
          ),
          'bonusModal'
        )
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetFreeSpin = useGetFreeSpinMutation({
    onSuccess: (res) => {
      if (res?.data?.freeSpinBonus.length > 0) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <FreeSpinModal data={res?.data?.freeSpinBonus} />
            </Suspense>
          ),
          'freeSpinModal'
        )
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  /** Profile Mutation with Bonus Logic **/
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      const userData = res?.data?.data
      if (userData) {
        user.setUserDetails(userData)
        user.setIsAuthenticate(true)
        setJackpotOn(userData?.isJackpotOptedIn)
        setJackpotMultiplier(userData?.jackpotMultiplier)

        if (!userData.isEmailVerified && !userData?.phoneVerified) {
          user.logout()
        }

        // Check if payment method allows bonus modals
        const isPaymentMethodRestricted = 
          paymentMethod === 'SKRILL' ||
          paymentMethod === 'PAY_BY_BANK' ||
          paymentMethod === 'TRUSTLY' ||
          paymentMethodFromStore === 'TRUSTLY'

        if (!isPaymentMethodRestricted && pathCookie) {
          // Show modals in priority order
          if (userData?.isPromotionBonusAllowed && !userData?.promotionBonusClaimedAt) {
            if (isAuthenticate) {
              mutationGetPromotionBonus.mutate()
            }
          } else if (!userData?.isTermsAccepted) {
            if (isAuthenticate) {
              portalStore.openPortal(
                () => (
                  <Suspense fallback={<ModalLoader />}>
                    <TermAndCondition />
                  </Suspense>
                ),
                'termsNConditionModal'
              )
            }
          } else if (userData?.isWelcomeBonusAllowed && !userData?.isWelcomeBonusClaimed) {
            if (isAuthenticate) {
              mutationGetWelcomeBonus.mutate()
            }
          } else if (!gamePlayUrl && userData?.isDailyBonusAllowed && !userData?.isDailyBonusClaimed) {
            if (isAuthenticate) {
              mutationGetDailyBonus.mutate()
            }
          } else if (!userData?.isScratchCardBonusClaimed) {
            if (isAuthenticate) {
              portalStore.openPortal(
                () => (
                  <Suspense fallback={<ModalLoader />}>
                    <ScratchCardComponent
                      scratchCardBonus={userData?.scratchCardBonusData?.scratchCardBonus}
                      userBonusId={userData?.scratchCardBonusData?.userBonusId}
                      rewardType={userData?.scratchCardBonusData?.rewardType}
                    />
                  </Suspense>
                ),
                'bonusStreak'
              )
            }
          } else if (userData?.isFreeSpinBonusApplicable) {
            if (isAuthenticate) {
              mutationGetFreeSpin.mutate()
            }
          }
        }
      }
    },
    onError: (error) => {
      if (error && error?.response?.data?.errors?.[0]?.name === 'UserNameDoesNotExistError') {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <UserNameModal />
            </Suspense>
          ),
          'loginModal'
        )
      }
      console.log('error', error)
    }
  })

  return {
    getProfileMutation,
    mutationGetDailyBonus,
    mutationGetWelcomeBonus,
    mutationGetPromotionBonus,
    mutationGetFreeSpin
  }
}

export default useProfileAndAuth
