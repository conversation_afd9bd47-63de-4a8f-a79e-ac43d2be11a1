import { useState, useEffect, useCallback } from 'react'
import { getCookie } from '../utils/cookiesCollection'
import { useUserStore } from '../store/useUserSlice'
import { useGetProfileMutation } from '../reactQuery'

/**
 * Secure authentication hook that validates real tokens instead of localStorage
 * This replaces the insecure localStorage.getItem('username') checks
 */
export const useSecureAuth = () => {
  const [isValidating, setIsValidating] = useState(true)
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    hasValidToken: false,
    user: null
  })

  const userStore = useUserStore()

  // Check if we have valid authentication tokens
  const hasValidTokens = useCallback(() => {
    const accessToken = getCookie('accessToken')
    const pathCookie = getCookie('path')
    
    // Must have both access token and path permission
    return !!(accessToken && pathCookie)
  }, [])

  // Profile mutation for token validation
  const profileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      const userData = res?.data?.data
      if (userData) {
        setAuthState({
          isAuthenticated: true,
          hasValidToken: true,
          user: userData
        })
        userStore.setUserDetails(userData)
        userStore.setIsAuthenticate(true)
      }
      setIsValidating(false)
    },
    onError: (error) => {
      // Token is invalid or expired
      setAuthState({
        isAuthenticated: false,
        hasValidToken: false,
        user: null
      })
      
      // Clear invalid state
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        userStore.logout()
      }
      setIsValidating(false)
    }
  })

  // Validate authentication on mount and token changes
  useEffect(() => {
    const validateAuth = async () => {
      setIsValidating(true)
      
      const hasTokens = hasValidTokens()
      
      if (!hasTokens) {
        // No tokens = definitely not authenticated
        setAuthState({
          isAuthenticated: false,
          hasValidToken: false,
          user: null
        })
        setIsValidating(false)
        return
      }

      // If we have tokens but no user data, validate with server
      if (hasTokens && !userStore.userDetails) {
        profileMutation.mutate()
      } else if (hasTokens && userStore.userDetails) {
        // We have tokens and user data - assume valid until proven otherwise
        setAuthState({
          isAuthenticated: true,
          hasValidToken: true,
          user: userStore.userDetails
        })
        setIsValidating(false)
      }
    }

    validateAuth()
  }, [hasValidTokens, userStore.userDetails, profileMutation])

  // Re-validate when cookies change (for cross-tab logout)
  useEffect(() => {
    const interval = setInterval(() => {
      const hasTokens = hasValidTokens()
      if (!hasTokens && authState.isAuthenticated) {
        // Tokens were removed (logout in another tab)
        setAuthState({
          isAuthenticated: false,
          hasValidToken: false,
          user: null
        })
        userStore.logout()
      }
    }, 5000) // Check every 5 seconds

    return () => clearInterval(interval)
  }, [hasValidTokens, authState.isAuthenticated, userStore])

  return {
    ...authState,
    isValidating,
    validateAuth: () => {
      if (hasValidTokens()) {
        profileMutation.mutate()
      }
    },
    logout: () => {
      setAuthState({
        isAuthenticated: false,
        hasValidToken: false,
        user: null
      })
      userStore.logout()
    }
  }
}

/**
 * Simple hook to check if user is authenticated (replaces localStorage checks)
 */
export const useIsAuthenticated = () => {
  const { isAuthenticated, isValidating } = useSecureAuth()
  return { isAuthenticated, isValidating }
}

/**
 * Hook for components that require authentication
 */
export const useRequireAuth = () => {
  const auth = useSecureAuth()
  
  if (!auth.isValidating && !auth.isAuthenticated) {
    throw new Error('Authentication required')
  }
  
  return auth
}

export default useSecureAuth
