import React, { Suspense } from 'react'
import { Navigate } from 'react-router-dom'
import { useSecureAuth } from '../hooks/useSecureAuth'
import Loader from './Loader'

/**
 * Secure route guard that validates real authentication tokens
 * instead of relying on localStorage values that can be manipulated
 */
const SecureRouteGuard = ({ 
  children, 
  requireAuth = false, 
  redirectTo = '/',
  fallback = <Loader />
}) => {
  const { isAuthenticated, isValidating, hasValidToken } = useSecureAuth()

  // Show loading while validating authentication
  if (isValidating) {
    return fallback
  }

  // For protected routes
  if (requireAuth) {
    if (!isAuthenticated || !hasValidToken) {
      return <Navigate to={redirectTo} replace />
    }
  }

  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  )
}

/**
 * Higher-order component for protecting routes
 */
export const withSecureAuth = (Component, options = {}) => {
  return function SecureAuthWrapper(props) {
    return (
      <SecureRouteGuard requireAuth={true} {...options}>
        <Component {...props} />
      </SecureRouteGuard>
    )
  }
}

/**
 * Component for routes that should only be accessible when NOT authenticated
 */
export const PublicOnlyRoute = ({ children, redirectTo = '/' }) => {
  const { isAuthenticated, isValidating } = useSecureAuth()

  if (isValidating) {
    return <Loader />
  }

  if (isAuthenticated) {
    return <Navigate to={redirectTo} replace />
  }

  return children
}

/**
 * Component that conditionally renders based on auth state
 */
export const AuthConditional = ({ 
  authenticated, 
  unauthenticated, 
  loading = <Loader /> 
}) => {
  const { isAuthenticated, isValidating } = useSecureAuth()

  if (isValidating) {
    return loading
  }

  return isAuthenticated ? authenticated : unauthenticated
}

export default SecureRouteGuard
